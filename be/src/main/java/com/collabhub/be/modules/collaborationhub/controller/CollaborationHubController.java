package com.collabhub.be.modules.collaborationhub.controller;

import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.collaborationhub.dto.*;
import com.collabhub.be.modules.collaborationhub.service.CollaborationHubService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.invoice.dto.PageResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;

/**
 * REST controller for collaboration hub management.
 * Provides endpoints for CRUD operations on collaboration hubs with proper authorization.
 */
@RestController
@RequestMapping("/api/hubs")
@Tag(name = "Collaboration Hubs", description = "Collaboration hub management operations")
public class CollaborationHubController {

    private static final Logger logger = LoggerFactory.getLogger(CollaborationHubController.class);

    private final CollaborationHubService hubService;
    private final JwtClaimsService jwtClaimsService;

    public CollaborationHubController(CollaborationHubService hubService, JwtClaimsService jwtClaimsService) {
        this.hubService = hubService;
        this.jwtClaimsService = jwtClaimsService;
    }

    /**
     * Creates a new collaboration hub.
     *
     * @param request the hub creation request
     * @param jwt the JWT token for authentication
     * @return the created hub response
     */
    @PostMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_WRITE.permission)")
    @Operation(summary = "Create a new collaboration hub",
               description = "Creates a new collaboration hub and automatically adds the creator as an admin participant")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Hub created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "409", description = "Hub name already exists")
    })
    public ResponseEntity<CollaborationHubResponse> createHub(
            @Valid @RequestBody CollaborationHubCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.info("Creating new collaboration hub: {} for account: {} by user: {}", 
                   request.getName(), userContext.getAccountId(), userContext.getUserId());

        CollaborationHubResponse response = hubService.createHub(
                request, userContext.getAccountId(), userContext.getUserId(), userContext.getEmail(), userContext.getDisplayName());

        logger.info("Successfully created collaboration hub with ID: {} for account: {}", 
                   response.getId(), userContext.getAccountId());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Gets a paginated list of collaboration hubs.
     *
     * @param name optional name filter (case-insensitive partial match)
     * @param brandId optional brand filter
     * @param page page number (0-based)
     * @param size page size (max 100)
     * @param jwt the JWT token for authentication
     * @return paginated list of hubs
     */
    @GetMapping
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_READ.permission)")
    @Operation(summary = "Get collaboration hubs",
               description = "Retrieves a paginated list of collaboration hubs with optional filtering")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Hubs retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    public ResponseEntity<PageResponse<CollaborationHubListItemDto>> getHubs(
            @Parameter(description = "Filter hubs by name (case-insensitive partial match)")
            @RequestParam(value = "name", required = false) String name,
            @Parameter(description = "Filter hubs by brand ID")
            @RequestParam(value = "brandId", required = false) Long brandId,
            @Parameter(description = "Page number (0-based)")
            @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "Page size (max 100)")
            @RequestParam(value = "size", defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving collaboration hubs with filters: name='{}', brandId={}, page={}, size={}",
                    name, brandId, page, size);

        PageRequest pageRequest = new PageRequest(page, size);
        PageResponse<CollaborationHubListItemDto> response = hubService.getHubs(
                pageRequest, name, brandId);

        logger.debug("Retrieved {} collaboration hubs", response.getContent().size());
        return ResponseEntity.ok(response);
    }

    /**
     * Gets detailed information about a specific collaboration hub.
     *
     * @param id the hub ID
     * @param jwt the JWT token for authentication
     * @return the hub details
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_READ.permission)")
    @Operation(summary = "Get collaboration hub details",
               description = "Retrieves detailed information about a specific collaboration hub")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Hub details retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Hub not found")
    })
    public ResponseEntity<CollaborationHubResponse> getHubDetails(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving collaboration hub details for hub: {}", id);

        CollaborationHubResponse response = hubService.getHubDetails(id);

        logger.debug("Retrieved collaboration hub details for hub: {}", id);
        return ResponseEntity.ok(response);
    }

    /**
     * Updates an existing collaboration hub.
     *
     * @param id the hub ID
     * @param request the update request
     * @param jwt the JWT token for authentication
     * @return the updated hub response
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_WRITE.permission)")
    @Operation(summary = "Update collaboration hub",
               description = "Updates an existing collaboration hub (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Hub updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Hub not found"),
        @ApiResponse(responseCode = "409", description = "Hub name already exists")
    })
    public ResponseEntity<CollaborationHubResponse> updateHub(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long id,
            @Valid @RequestBody CollaborationHubUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.info("Updating collaboration hub: {}", id);

        CollaborationHubResponse response = hubService.updateHub(id, request);

        logger.info("Successfully updated collaboration hub: {}", id);
        return ResponseEntity.ok(response);
    }

    /**
     * Deletes a collaboration hub.
     *
     * @param id the hub ID
     * @param jwt the JWT token for authentication
     * @return no content response
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).HUB_DELETE.permission)")
    @Operation(summary = "Delete collaboration hub",
               description = "Deletes a collaboration hub and all associated data (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Hub deleted successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Hub not found")
    })
    public ResponseEntity<Void> deleteHub(
            @Parameter(description = "Hub ID", required = true)
            @PathVariable Long id,
            @AuthenticationPrincipal Jwt jwt) {

        logger.info("Deleting collaboration hub: {}", id);

        hubService.deleteHub(id);

        logger.info("Successfully deleted collaboration hub: {}", id);
        return ResponseEntity.noContent().build();
    }
}
