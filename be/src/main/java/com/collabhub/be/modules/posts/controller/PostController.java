package com.collabhub.be.modules.posts.controller;

import com.collabhub.be.config.S3Properties;
import com.collabhub.be.modules.auth.dto.UserContext;
import com.collabhub.be.modules.auth.service.JwtClaimsService;
import com.collabhub.be.modules.invoice.dto.PageRequest;
import com.collabhub.be.modules.posts.constants.PostConstants;
import com.collabhub.be.modules.posts.dto.*;
import com.collabhub.be.modules.posts.service.PostReviewService;
import com.collabhub.be.modules.posts.service.PostService;
import com.collabhub.be.modules.media.service.MediaService;
import com.collabhub.be.modules.media.dto.MediaDto;
import com.collabhub.be.service.s3.S3StorageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.jooq.generated.enums.ReviewStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.collabhub.be.exception.BadRequestException;
import com.collabhub.be.exception.ErrorCode;

/**
 * REST controller for managing posts in collaboration hubs.
 * Provides endpoints for CRUD operations, media upload, and filtering.
 */
@RestController
@RequestMapping("/api")
@Tag(name = "Posts", description = "Post management in collaboration hubs")
public class PostController {

    private static final Logger logger = LoggerFactory.getLogger(PostController.class);

    private final PostService postService;
    private final PostReviewService postReviewService;
    private final S3StorageService s3StorageService;
    private final MediaService mediaService;
    private final JwtClaimsService jwtClaimsService;

    public PostController(PostService postService, PostReviewService postReviewService, S3StorageService s3StorageService, MediaService mediaService, JwtClaimsService jwtClaimsService) {
        this.postService = postService;
        this.postReviewService = postReviewService;
        this.s3StorageService = s3StorageService;
        this.mediaService = mediaService;
        this.jwtClaimsService = jwtClaimsService;
    }

    @Operation(
        summary = "Create a new post",
        description = "Creates a new post in a collaboration hub. Post must have either caption or media content (or both). " +
                     "Only content creators, admins, and reviewer-creators can create posts.",
        responses = {
            @ApiResponse(responseCode = "201", description = "Post created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
            @ApiResponse(responseCode = "403", description = "User lacks permission to create posts in this hub"),
            @ApiResponse(responseCode = "404", description = "Hub not found")
        }
    )
    @PostMapping("/hubs/{hubId}/posts")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<PostResponse> createPost(
            @Parameter(description = "Hub ID") @PathVariable Long hubId,
            @Valid @RequestBody PostCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Creating post in hub {}", hubId);

        PostResponse response = postService.createPost(hubId, request);

        logger.debug("Successfully created post {} in hub {}", response.getId(), hubId);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "List posts", description = "Gets a paginated list of posts with filtering")
    @GetMapping("/hubs/{hubId}/posts")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_READ.permission)")
    public ResponseEntity<PostListResponse> getPosts(
            @Parameter(description = "Hub ID") @PathVariable Long hubId,
            @Parameter(description = "Filter type") @RequestParam(value = "filter", defaultValue = PostConstants.DEFAULT_FILTER) String filter,
            @Parameter(description = "Review status filter") @RequestParam(value = "status", required = false) ReviewStatus status,
            @Parameter(description = "Page number (0-based)") @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "Page size (max 100)") @RequestParam(value = "size", defaultValue = "20") int size,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving posts for hub {} with filter: {}, status: {}", hubId, filter, status);

        PageRequest pageRequest = new PageRequest(page, size);
        PostListResponse response = postService.getPosts(hubId, pageRequest, filter, status);

        logger.debug("Retrieved {} posts for hub {}", response.getContent().size(), hubId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get post details", description = "Gets detailed information about a specific post")
    @GetMapping("/posts/{postId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_READ.permission)")
    public ResponseEntity<PostResponse> getPost(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Retrieving post details for post {}", postId);

        PostResponse response = postService.getPostDetails(postId);

        logger.debug("Successfully retrieved post details for post {}", postId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Update a post", description = "Updates an existing post")
    @PutMapping("/posts/{postId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_UPDATE.permission)")
    public ResponseEntity<PostResponse> updatePost(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @Valid @RequestBody PostUpdateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Updating post {}", postId);

        PostResponse response = postService.updatePost(postId, request);

        logger.debug("Successfully updated post {}", postId);
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Delete a post",
        description = "Soft deletes a post and removes associated media files from S3. " +
                     "Only post creators and hub admins can delete posts.",
        responses = {
            @ApiResponse(responseCode = "204", description = "Post deleted successfully"),
            @ApiResponse(responseCode = "403", description = "User lacks permission to delete this post"),
            @ApiResponse(responseCode = "404", description = "Post not found")
        }
    )
    @DeleteMapping("/posts/{postId}")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_DELETE.permission)")
    public ResponseEntity<Void> deletePost(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @AuthenticationPrincipal Jwt jwt) {

        logger.debug("Deleting post {}", postId);

        postService.deletePost(postId);

        logger.debug("Successfully deleted post {}", postId);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Upload media file", description = "Uploads a media file (image or video) for posts")
    @PostMapping(value = "/posts/media/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<FileUploadResponse> uploadMedia(
            @Parameter(description = "Media file to upload") @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Uploading media file: {} for account {}", file.getOriginalFilename(), userContext.getAccountId());

        FileUploadResponse response = postService.uploadMedia(file, userContext.getAccountId());

        logger.debug("Successfully uploaded media file: {}", response.getUrl());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(summary = "Generate presigned upload URL", description = "Generates a presigned URL for direct S3 upload with enforced constraints")
    @PostMapping("/posts/media/presigned-url")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<PresignedUploadResponse> generatePresignedUploadUrl(
            @Parameter(description = "File name") @RequestParam("fileName") String fileName,
            @Parameter(description = "Content type") @RequestParam("contentType") String contentType,
            @Parameter(description = "Max file size in bytes (optional)") @RequestParam(value = "maxFileSize", required = false) Long maxFileSize,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Generating presigned URL for file: {} with type: {} for account {}",
                    fileName, contentType, userContext.getAccountId());

        PresignedUploadResponse response = s3StorageService.generatePresignedUploadUrl(
                userContext.getAccountId(), S3Properties.ResourceType.POSTS, fileName, contentType, maxFileSize);

        logger.debug("Successfully generated presigned URL for file: {}", fileName);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Generate batch presigned upload URLs", description = "Generates multiple presigned URLs for concurrent S3 uploads with enforced constraints")
    @PostMapping("/posts/media/batch-presigned-url")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<BatchPresignedUploadResponse> generateBatchPresignedUploadUrls(
            @Parameter(description = "Batch upload request") @Valid @RequestBody BatchPresignedUploadRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Generating {} presigned URLs for account {}",
                    request.getFiles().size(), userContext.getAccountId());

        List<PresignedUploadResponse> uploadUrls = new ArrayList<>();

        for (BatchPresignedUploadRequest.FileUploadRequest fileRequest : request.getFiles()) {
            try {
                PresignedUploadResponse uploadUrl = s3StorageService.generatePresignedUploadUrl(
                        userContext.getAccountId(),
                        S3Properties.ResourceType.POSTS,
                        fileRequest.getFileName(),
                        fileRequest.getContentType(),
                        fileRequest.getMaxFileSize()
                );
                uploadUrls.add(uploadUrl);
            } catch (Exception e) {
                logger.error("Failed to generate presigned URL for file: {} - {}",
                           fileRequest.getFileName(), e.getMessage());
                throw new BadRequestException(ErrorCode.INVALID_INPUT,
                    "Failed to generate presigned URL for file: " + fileRequest.getFileName());
            }
        }

        BatchPresignedUploadResponse response = new BatchPresignedUploadResponse(uploadUrls);
        logger.debug("Successfully generated {} presigned URLs for account {}",
                    uploadUrls.size(), userContext.getAccountId());

        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Create media record after presigned upload", description = "Creates a media record after successful presigned URL upload")
    @PostMapping("/posts/media/create-record")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<FileUploadResponse> createMediaRecord(
            @Parameter(description = "Media creation request") @Valid @RequestBody MediaRecordCreateRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Creating media record for URL: {} for account {}",
                    request.getFileUrl(), userContext.getAccountId());

        try {
            // Create media record from the uploaded file URL
            MediaDto mediaDto = mediaService.createMediaFromUrl(
                    request.getFileUrl(),
                    request.getFilename(),
                    request.getFileSize(),
                    request.getMimeType(),
                    userContext.getAccountId()
            );

            // Determine file type for response
            FileType type = FileType.fromMimeType(request.getMimeType());

            FileUploadResponse response = new FileUploadResponse(
                    mediaDto.getUrl(),
                    request.getFilename(),
                    request.getFileSize(),
                    request.getMimeType(),
                    type
            );

            logger.debug("Successfully created media record for URL: {} with ID: {}",
                        request.getFileUrl(), mediaDto.getId());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Failed to create media record for URL: {} - {}", request.getFileUrl(), e.getMessage());
            throw new BadRequestException(ErrorCode.INVALID_INPUT,
                "Failed to create media record: " + e.getMessage());
        }
    }

    @Operation(summary = "Validate uploaded file", description = "Validates that a file was uploaded successfully via presigned URL")
    @PostMapping("/posts/media/validate")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_WRITE.permission)")
    public ResponseEntity<FileValidationResponse> validateUploadedFile(
            @Parameter(description = "File URL to validate") @RequestParam("fileUrl") String fileUrl,
            @Parameter(description = "Expected content type") @RequestParam(value = "contentType", required = false) String contentType,
            @Parameter(description = "Max file size") @RequestParam(value = "maxFileSize", required = false) Long maxFileSize,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Validating uploaded file: {} for account {}", fileUrl, userContext.getAccountId());

        FileValidationResponse response = s3StorageService.validateUploadedFileDetailed(
                fileUrl, userContext.getAccountId(), contentType, maxFileSize);

        logger.debug("File validation result for {}: {}", fileUrl, response.getValid());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Generate presigned download URL", description = "Generates a presigned URL for secure file download")
    @PostMapping("/posts/media/download-url")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).POST_READ.permission)")
    public ResponseEntity<Map<String, String>> generatePresignedDownloadUrl(
            @Parameter(description = "File URL to download") @RequestParam("fileUrl") String fileUrl,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Generating presigned download URL for file: {} for account {}", fileUrl, userContext.getAccountId());

        String presignedUrl = s3StorageService.generatePresignedDownloadUrl(fileUrl, userContext.getAccountId());

        Map<String, String> response = Map.of("downloadUrl", presignedUrl);
        logger.debug("Successfully generated presigned download URL for file: {}", fileUrl);
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Submit or update a post review",
        description = "Submits or updates a review for a post. Only assigned reviewers and hub admins can review posts. " +
                     "Uses upsert behavior - one review per reviewer per post.",
        responses = {
            @ApiResponse(responseCode = "200", description = "Review submitted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
            @ApiResponse(responseCode = "403", description = "User lacks permission to review this post"),
            @ApiResponse(responseCode = "404", description = "Post not found")
        }
    )
    @PostMapping("/posts/{postId}/reviews")
    @PreAuthorize("hasAuthority(T(com.collabhub.be.modules.auth.model.Permission).CONTENT_REVIEW.permission)")
    public ResponseEntity<PostReviewResponse> submitReview(
            @Parameter(description = "Post ID") @PathVariable Long postId,
            @Valid @RequestBody PostReviewRequest request,
            @AuthenticationPrincipal Jwt jwt) {

        UserContext userContext = jwtClaimsService.extractUserContext(jwt);
        logger.debug("Submitting review for post {} by user {}", postId, userContext.getUserId());

        PostReviewResponse response = postReviewService.submitReview(postId, request, userContext);

        logger.debug("Successfully submitted review for post {} by user {}", postId, userContext.getUserId());
        return ResponseEntity.ok(response);
    }
}
